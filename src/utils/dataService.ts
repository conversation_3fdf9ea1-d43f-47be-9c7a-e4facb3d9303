/**
 * Data service that uses the API utilities to fetch data
 * This service maintains compatibility with the existing data structure
 */

import {
  fetchHotels,
  fetchFeaturedHotels,
  fetchHotelBySlug,
  fetchHotelsByDestination,
  fetchHotelDetailsById,
  fetchHotelAvailability,
  fetchHotelsList,
  fetchHotelsBySkiingType,
  type Hotel as ApiHotel,
} from "./store/hotels";

import {
  getDefaultCheckInDate,
  getDefaultCheckOutDate,
  formatDate,
  formatDateForAPI,
} from "./dateUtils";

import {
  fetchDestinations,
  fetchFeaturedDestinations,
  fetchDestinationBySlug,
  type Destination as ApiDestination,
  type DestinationFAQ,
} from "./store/destinations";

import { getCurrentCurrencyCode } from "./currencyHelper";

/**
 * Maps API hotel data to the format expected by the UI components
 */
const mapHotelToUiFormat = (hotel: ApiHotel) => {
  const mappedHotel = {
    id: hotel.id,
    name: hotel.name,
    description: hotel.description,
    location: hotel.location,
    destination: hotel.handle?.split("-")[0] || "",
    destination_id: hotel.destination_id || "",
    rating: hotel.rating,
    price: hotel.price,
    currency: hotel.currency || "USD", // Use hotel currency if available, otherwise default to $
    imageUrl: (() => {
      if (!Array.isArray(hotel.images) || hotel.images.length === 0) return "";

      // Find thumbnail image first
      const thumbnailImage = hotel.images.find((img: any) =>
        img && typeof img === "object" && img.metadata?.isThumbnail === true
      );

      if (thumbnailImage) {
        return (thumbnailImage as any).url || "";
      }

      // If no thumbnail, use the first image sorted by rank
      const sortedImages = hotel.images
        .filter((img: any) => img && typeof img === "object" && img.url)
        .sort((a: any, b: any) => (a.rank || 0) - (b.rank || 0));

      return sortedImages.length > 0 ? sortedImages[0]?.url : "";
    })(),
    images: Array.isArray(hotel.images)
      ? hotel.images
          .filter((img: any) => img && typeof img === "object" && img.url) // Only keep valid image objects
          .sort((a: any, b: any) => (a.rank || 0) - (b.rank || 0)) // Sort by rank
          .map((img: any) => img.url) // Extract URLs
      : [],
    amenities: hotel.amenities || [],
    rules: hotel?.rules || [],
    safety_measures: hotel?.safety_measures || [],
    featured: hotel.is_featured || true, // Use is_featured if available
    handle: hotel.handle,
    stars: hotel.stars,
    reviews: hotel.reviews,
    tags: hotel.tags,
    rooms: hotel.rooms?.map((room) => ({
      id: room.id,
      name: room.name,
      description: room.description,
      price: room.price,
      size: room.size,
      image: typeof room.image === "string" ? room.image : room.image.url,
      perks: room.perks,
    })),
    check_in_time: hotel.check_in_time,
    check_out_time: hotel.check_out_time,
    cancellation_policies: hotel.cancellation_policies,
    room_configurations: hotel.room_configurations,
  };

  return mappedHotel;
};

/**
 * Maps API destination data to the format expected by the UI components
 * with improved error handling and no hardcoded fallbacks
 */
const mapDestinationToUiFormat = (destination: ApiDestination) => {
  // Extract images with error handling and prioritize thumbnail
  const extractImages = (images: any[] | undefined) => {
    if (!Array.isArray(images) || images.length === 0) {
      return [];
    }

    // Separate thumbnail and regular images
    const thumbnailImages: string[] = [];
    const regularImages: string[] = [];

    images.forEach((img) => {
      let imageUrl = "";

      if (typeof img === "string") {
        imageUrl = img;
      } else if (img && typeof img === "object" && img.url) {
        imageUrl = img.url;
      }

      if (imageUrl) {
        // Check if this is a thumbnail image
        if (img && typeof img === "object" && img.metadata?.isThumbnail === true) {
          thumbnailImages.push(imageUrl);
        } else {
          regularImages.push(imageUrl);
        }
      }
    });

    // Return thumbnail images first, then regular images
    return [...thumbnailImages, ...regularImages];
  };

  // Get the first valid image or fallback to a placeholder
  const getMainImage = (images: string[]) => {
    return images.length > 0
      ? images[0]
      : "/images/placeholder-destination.jpg";
  };

  // Extract all images
  const allImages = extractImages(destination.images);

  return {
    id: destination.handle || destination.slug || destination.id,
    name: destination.name || "Unnamed Destination",
    description:
      destination.description ||
      "Discover this extraordinary destination with Perfect Piste.",
    country: destination.country || "",
    propertyCount: destination.property_count || 0,
    imageUrl: getMainImage(allImages),
    featured: destination.is_featured || false,
    activities: destination.activities || [],
    handle: destination.handle || destination.slug,
    images: allImages,
    faqs: destination.faqs || [],
  };
};

/**
 * Get all hotels with API
 */
export const getAllHotels = async () => {
  try {
    const { hotels } = await fetchHotels({ limit: 50 });
    return hotels.map(mapHotelToUiFormat);
  } catch (error) {
    console.error("Error fetching hotels:", error);
    throw error;
  }
};

/**
 * Get all hotels using the list endpoint
 */
export const getHotelsList = async () => {
  try {
    const { hotels } = await fetchHotelsList();
    return hotels;
  } catch (error) {
    console.error("Error fetching hotels list:", error);
    throw error;
  }
};

/**
 * Get featured hotels with API
 */
export const getFeaturedHotels = async (count: number = 3) => {
  try {
    const hotels = await fetchFeaturedHotels(count);
    const mappedHotels = hotels.map(mapHotelToUiFormat);
    return mappedHotels;
  } catch (error) {
    console.error("Error fetching featured hotels:", error);
    throw error;
  }
};

/**
 * Get hotel by ID with API
 */
export const getHotelById = async (id: number | string) => {
  try {
    // If id is a string and looks like a UUID (contains hyphens or is long), use it directly with the details API
    if (typeof id === "string" && (id.includes("-") || id.length > 20)) {
      const { hotel } = await fetchHotelDetailsById(id);
      return mapHotelToUiFormat(hotel);
    }

    // Otherwise, use the slug/handle directly
    const hotelSlug = typeof id === "number" ? id.toString() : id;

    if (!hotelSlug) {
      throw new Error("Invalid hotel ID");
    }

    const hotel = await fetchHotelBySlug(hotelSlug.toString());
    return mapHotelToUiFormat(hotel);
  } catch (error) {
    console.error(`Error fetching hotel with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Get all destinations with API
 */
export const getAllDestinations = async () => {
  try {
    const { destinations } = await fetchDestinations({ limit: 50 });
    return destinations.map(mapDestinationToUiFormat);
  } catch (error) {
    console.error("Error fetching destinations:", error);
    throw error;
  }
};

/**
 * Get destination by ID with API
 */
export const getDestinationById = async (id: string) => {
  try {
    const destination = await fetchDestinationBySlug(id);
    return destination ? mapDestinationToUiFormat(destination) : null;
  } catch (error) {
    console.error(`Error fetching destination with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Get hotels by destination with API
 */
export const getHotelsByDestination = async (
  destinationId: string,
  isFeatured: boolean = false
) => {
  try {
    const { hotels } = await fetchHotelsByDestination(
      destinationId,
      10,
      isFeatured
    );
    return hotels.map(mapHotelToUiFormat);
  } catch (error) {
    console.error(
      `Error fetching hotels for destination ${destinationId}:`,
      error
    );
    throw error;
  }
};

/**
 * Get hotel details including room configurations and cancellation policies
 */
export const getHotelDetails = async (id: string) => {
  try {
    const { hotel, room_configurations } = await fetchHotelDetailsById(id);
    return {
      hotel: mapHotelToUiFormat(hotel),
      roomConfigurations: room_configurations,
    };
  } catch (error) {
    console.error(`Error fetching hotel details for ID ${id}:`, error);
    throw error;
  }
};

/**
 * Get hotel and room types in a single API call
 * This optimized function fetches both hotel details and room configurations with one API call
 * and also fetches availability and pricing information
 * @param id - The hotel ID
 * @param checkInDate - Optional check-in date (defaults to tomorrow)
 * @param checkOutDate - Optional check-out date (defaults to tomorrow + 4 days)
 * @param adults - Optional number of adults (defaults to 1)
 * @param children - Optional number of children (defaults to 0)
 * @param infants - Optional number of infants (defaults to 0)
 */
/**
 * Get hotel availability data only - optimized for date changes
 * This function only calls the availability API without fetching hotel details again
 * @param id - The hotel ID
 * @param checkInDate - Check-in date
 * @param checkOutDate - Check-out date
 * @param adults - Number of adults (defaults to 1)
 * @param children - Number of children (defaults to 0)
 * @param infants - Number of infants (defaults to 0)
 * @param currencyCode - Currency code (defaults to selected currency from currency selector)
 */
export const getHotelAvailabilityOnly = async (
  id: number | string,
  checkInDate: Date | null,
  checkOutDate: Date | null,
  adults: number = 1,
  children: number = 0,
  infants: number = 0,
  currencyCode: string = getCurrentCurrencyCode()
) => {
  if (!checkInDate || !checkOutDate) {
    throw new Error("Check-in and check-out dates are required");
  }

  try {
    // Call the availability API directly
    const availabilityData = await fetchHotelAvailability(
      id.toString(),
      formatDateForAPI(checkInDate),
      formatDateForAPI(checkOutDate),
      adults,
      children,
      infants,
      currencyCode
    );

    // Map the available rooms to the expected format
    const mappedRoomTypes = availabilityData.available_rooms.map((room) => ({
      id: room.id,
      name: room.title,
      description: room.description || "",
      price: room.price?.per_night_amount || 0,
      maxGuests: room.max_occupancy,
      maxAdults: room.max_adults,
      maxChildren: room.max_children,
      maxInfants: room.max_infants,
      bedType: room.bed_type,
      size: room.room_size,
      amenities: room.amenities || [],
      images: room.images?.map((img: any) => img.url) || [],
      thumbnail:
        room.thumbnail || (room.images?.length > 0 ? room.images[0].url : null),
      available: room.available || false,
      availableRooms: room.available_rooms || 0,
      priceDetails: room.price || null,
      extra_adults_beyond_capacity: room.extra_adults_beyond_capacity,
      nights: availabilityData.nights || 0,
      currencyCode: getCurrentCurrencyCode(),
    }));

    return {
      roomTypes: mappedRoomTypes,
      checkIn: availabilityData.check_in,
      checkOut: availabilityData.check_out,
      nights: availabilityData.nights,
    };
  } catch (error) {
    console.error(`Error fetching hotel availability for ID ${id}:`, error);
    throw error;
  }
};

/**
 * Get hotel details quickly without room availability (for fast page load)
 */
export const getHotelDetailsQuick = async (id: number | string) => {
  try {
    // If id is a string and looks like a UUID (contains hyphens or is long), use it directly with the details API
    if (typeof id === "string" && (id.includes("-") || id.length > 20)) {
      const { hotel, room_configurations } = await fetchHotelDetailsById(id);

      // Map room configurations to basic format without availability data
      const basicRoomTypes =
        room_configurations && room_configurations.length > 0
          ? room_configurations.map((room) => ({
              id: room.id,
              name: room.title,
              description: room.description || "",
              price: null, // Will be populated by availability call
              maxGuests: room.room_details?.max_occupancy || room.max_occupancy,
              bedType: room.room_details?.bed_type || room.bed_type,
              size: room.room_details?.room_size || room.room_size,
              maxAdults: room.room_details?.max_adults || room.max_adults,
              maxChildren: room.room_details?.max_children || room.max_children,
              maxInfants: room.room_details?.max_infants || room.max_infants,
              maxExtraBeds: room.room_details?.max_extra_beds || room.max_extra_beds,
              amenities: room.amenities || [],
              images: room.images?.length > 0 ? room.images.map((img) => img.url) : [],
              thumbnail: room.thumbnail || (room.images?.length > 0 ? room.images[0].url : null),
              // Default availability state (will be updated by separate call)
              available: true,
              availableRooms: 0,
              priceDetails: null,
              nights: 0,
              currencyCode: getCurrentCurrencyCode(),
              isLoading: true, // Flag to indicate this room is still loading availability
            }))
          : [];

      return {
        hotel: mapHotelToUiFormat(hotel),
        roomTypes: basicRoomTypes,
        checkIn: formatDate(getDefaultCheckInDate()),
        checkOut: formatDate(getDefaultCheckOutDate()),
        nights: 2,
      };
    } else {
      // For non-UUID IDs, use the regular hotel fetch
      const hotel = await getHotelById(id);
      return {
        hotel,
        roomTypes: [],
        checkIn: formatDate(getDefaultCheckInDate()),
        checkOut: formatDate(getDefaultCheckOutDate()),
        nights: 2,
      };
    }
  } catch (error) {
    console.error(`Error fetching hotel details for ID ${id}:`, error);
    throw error;
  }
};

/**
 * Get room availability data separately (for async loading)
 */
export const getRoomAvailability = async (
  id: number | string,
  checkInDate?: Date | null,
  checkOutDate?: Date | null,
  adults: number = 1,
  children: number = 0,
  infants: number = 0,
  currencyCode: string = getCurrentCurrencyCode()
) => {
  try {
    // Only works with UUID-style IDs
    if (typeof id === "string" && (id.includes("-") || id.length > 20)) {
      const defaultCheckIn = checkInDate || getDefaultCheckInDate();
      const defaultCheckOut = checkOutDate || getDefaultCheckOutDate();

      const availabilityData = await fetchHotelAvailability(
        id,
        formatDateForAPI(defaultCheckIn),
        formatDateForAPI(defaultCheckOut),
        adults,
        children,
        infants,
        currencyCode
      );

      // Create a map of room IDs to availability data for quick lookup
      const availabilityMap = new Map();
      if (availabilityData && availabilityData.available_rooms) {
        availabilityData.available_rooms.forEach((room) => {
          availabilityMap.set(room.id, room);
        });
      }

      return {
        availabilityMap,
        checkIn: availabilityData?.check_in || formatDate(defaultCheckIn),
        checkOut: availabilityData?.check_out || formatDate(defaultCheckOut),
        nights: availabilityData?.nights || 2,
      };
    }

    return null;
  } catch (error) {
    console.error(`Error fetching room availability for ID ${id}:`, error);
    throw error;
  }
};

export const getHotelAndRoomTypes = async (
  id: number | string,
  checkInDate?: Date | null,
  checkOutDate?: Date | null,
  adults: number = 1,
  children: number = 0,
  infants: number = 0
) => {
  try {
    // If id is a string and looks like a UUID (contains hyphens or is long), use it directly with the details API
    if (typeof id === "string" && (id.includes("-") || id.length > 20)) {
      // First, get the hotel details and room configurations
      const { hotel, room_configurations } = await fetchHotelDetailsById(id);

      // Then, get availability and pricing information
      const defaultCheckIn = checkInDate || getDefaultCheckInDate();
      const defaultCheckOut = checkOutDate || getDefaultCheckOutDate();

      let availabilityData;
      try {
        availabilityData = await fetchHotelAvailability(
          id,
          formatDateForAPI(defaultCheckIn),
          formatDateForAPI(defaultCheckOut),
          adults, // Use provided adults or default
          children, // Use provided children or default
          infants, // Use provided infants or default
          getCurrentCurrencyCode() // Use selected currency from currency selector
        );
      } catch (availabilityError) {
        console.error("Error fetching availability data:", availabilityError);
        // Continue without availability data
        availabilityData = null;
      }

      // Create a map of room IDs to availability data for quick lookup
      const availabilityMap = new Map();
      if (availabilityData && availabilityData.available_rooms) {
        availabilityData.available_rooms.forEach((room) => {
          availabilityMap.set(room.id, room);
        });
      }

      // Map room configurations to the expected format, including availability data
      const mappedRoomTypes =
        room_configurations && room_configurations.length > 0
          ? room_configurations
              .map((room) => {
                // Get availability data for this room if available
                const availabilityInfo = availabilityMap.get(room.id);
                return {
                  id: room.id,
                  name: room.title,
                  description: room.description || "",
                  // Use price from availability data if available
                  price: availabilityInfo?.price?.per_night_amount || null,
                  maxGuests:
                    room.room_details?.max_occupancy || room.max_occupancy,
                  bedType: room.room_details?.bed_type || room.bed_type,
                  size: room.room_details?.room_size || room.room_size,
                  // Show all max-related data
                  maxAdults: room.room_details?.max_adults || room.max_adults,
                  maxChildren:
                    room.room_details?.max_children || room.max_children,
                  maxInfants: room.room_details?.max_infants || room.max_infants,
                  maxExtraBeds:
                    room.room_details?.max_extra_beds || room.max_extra_beds,
                  amenities: room.amenities || [],
                  images:
                    room.images?.length > 0
                      ? room.images.map((img) => img.url)
                      : [],
                  thumbnail:
                    room.thumbnail ||
                    (room.images?.length > 0 ? room.images[0].url : null),
                  // Add availability information
                  available: availabilityInfo?.available || false,
                  availableRooms: availabilityInfo?.available_rooms || 0,
                  priceDetails: availabilityInfo?.price || null,
                  extra_adults_beyond_capacity: availabilityInfo?.extra_adults_beyond_capacity,
                  nights: availabilityData?.nights || 0,
                  currencyCode: getCurrentCurrencyCode(),
                };
              })
              .filter((room) => {
                // Filter out rooms without valid pricing information
                const hasPrice = room.price && room.price > 0;
                const hasPriceDetails = room.priceDetails?.per_night_amount && room.priceDetails.per_night_amount > 0;
                return hasPrice || hasPriceDetails;
              })
          : []; // Return empty array if no room configurations

      return {
        hotel: mapHotelToUiFormat(hotel),
        roomTypes: mappedRoomTypes,
        checkIn: availabilityData?.check_in || formatDate(defaultCheckIn),
        checkOut: availabilityData?.check_out || formatDate(defaultCheckOut),
        nights: availabilityData?.nights || 2,
      };
    } else {
      // For non-UUID IDs, use the regular hotel fetch but don't use static room types
      const hotel = await getHotelById(id);
      return {
        hotel,
        roomTypes: [], // Return empty array instead of static data
        checkIn: formatDate(getDefaultCheckInDate()),
        checkOut: formatDate(getDefaultCheckOutDate()),
        nights: 2,
      };
    }
  } catch (error) {
    console.error(`Error fetching hotel and room types for ID ${id}:`, error);
    // Don't use fallback data, just throw the error
    throw error;
  }
};

/**
 * Get room types from API without using fallback data
 */
export const getRoomTypes = async (hotelId?: string) => {
  if (hotelId) {
    try {
      // Try to get room configurations from API
      const { roomConfigurations } = await getHotelDetails(hotelId);

      if (roomConfigurations && roomConfigurations.length > 0) {
        // Map room configurations to the expected format
        return roomConfigurations.map((room) => ({
          id: room.id,
          name: room.title,
          description: room.description || "",
          price: null, // API doesn't provide price
          maxGuests: room.max_occupancy,
          bedType: room.bed_type,
          size: room.room_size,
          amenities: room.amenities || [],
          images:
            room.images?.length > 0 ? room.images.map((img) => img.url) : [],
        }));
      }

      // Return empty array if no room configurations
      return [];
    } catch (error) {
      console.error(
        `Error fetching room types for hotel ID ${hotelId}:`,
        error
      );
      throw error;
    }
  }

  // Return empty array if no hotel ID provided
  return [];
};

/**
 * Get hotels by skiing type/category
 */
export const getHotelsBySkiingType = async (
  skiingType: string,
  limit: number = 6,
  isFeatured: boolean = false
) => {
  try {
    const { hotels } = await fetchHotelsBySkiingType(skiingType, limit, isFeatured);
    return hotels.map(mapHotelToUiFormat);
  } catch (error) {
    console.error(
      `Error fetching hotels for skiing type ${skiingType}:`,
      error
    );
    throw error;
  }
};

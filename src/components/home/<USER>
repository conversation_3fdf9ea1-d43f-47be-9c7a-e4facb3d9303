---
import SectionIntroWithMicro from "./SectionIntroWithMicro.tsx";
import { getFeaturedHotels } from "../../utils/dataService";
import SimpleHotelCard from "../hotels/SimpleHotelCard.astro";
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";

interface Props {
  lang: Lang;
}

const { lang } = Astro.props;
const t = useTranslations(lang);

const featuredHotels = await getFeaturedHotels(8);
---

<section>
  <div class="container-custom max-w-[1400px] mx-auto">
    <SectionIntroWithMicro
      microHeadline={t("home.featuredStays.microHeadline")}
      microHeadlineColor="#285DA6"
      title={t("home.featuredStays.title")}
      subtitle={t("home.featuredStays.subtitle")}
      className="featured-section"
      client:visible
    />

    <style>
      :global(.section-intro) {
        max-width: 100%;
        width: 100%;
        margin-bottom: 1rem; /* Reduced from 3rem to 1rem */
      }

      :global(.section-title) {
        white-space: normal;
        font-size: clamp(1.25rem, 1.5vw, 1.8rem);
        overflow: visible;
        max-width: 100%;
        text-align: center;
      }

      :global(.section-subtitle) {
        font-size: 0.875rem;
        line-height: 1.5rem;
        font-weight: 300;
      }

      @media (min-width: 640px) {
        :global(.section-intro) {
          margin-bottom: 1.5rem;
        }
        :global(.section-title) {
          white-space: nowrap;
        }
      }

      @media (max-width: 1200px) {
        :global(.section-title) {
          font-size: clamp(1.2rem, 1.4vw, 1.4rem);
          letter-spacing: 0.08em;
        }
      }
    </style>

    <div
      class="flex justify-center sm:justify-end items-center mb-6 cursor-pointer"
    >
      <a
        href="/inspiration"
        class="inline-flex items-center cursor-pointer font-karla font-bold text-xs uppercase tracking-[0.05em] text-[#285DA6] border-b border-[#285DA6] pb-1 transition-all hover:border-transparent whitespace-nowrap"
      >
        {t("home.featuredStays.viewAllStays")}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>
    </div>

    <!-- Carousel Container -->
    <div class="relative max-w-[1400px] mx-auto">
      <!-- Carousel Track -->
      <div class="overflow-hidden">
        <div
          id="carouselTrack"
          class="flex transition-transform duration-300 ease-in-out gap-4"
          style="transform: translateX(0px)"
        >
          {
            featuredHotels.map((hotel) => {
              const locationParts = hotel.location
                ? hotel.location.split(",")
                : [];
              const country =
                locationParts.length > 1
                  ? locationParts[locationParts.length - 1].trim()
                  : "";
              const resort =
                locationParts.length > 0 ? locationParts[0].trim() : "";

              return (
                <div class="flex-none w-[calc(25%-12px)] sm:w-[calc(50%-8px)] md:w-[calc(33.333%-11px)] lg:w-[calc(25%-12px)] min-w-[240px]">
                  <SimpleHotelCard
                    id={hotel.id}
                    name={hotel.name}
                    location={hotel.location}
                    imageUrl={hotel?.imageUrl}
                    country={country}
                    resort={resort}
                  />
                </div>
              );
            })
          }
        </div>
      </div>

      <!-- Navigation Arrows - Moved to bottom -->
      <div class="flex justify-center mt-6 mb-2 space-x-4">
        <button
          id="prevBtn"
          class="bg-white/90 hover:bg-white shadow-lg rounded-full p-3 transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Previous hotels"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="text-[#285DA6]"
          >
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </button>

        <button
          id="nextBtn"
          class="bg-white/90 hover:bg-white shadow-lg rounded-full p-3 transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Next hotels"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="text-[#285DA6]"
          >
            <polyline points="9 6 15 12 9 18"></polyline>
          </svg>
        </button>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const track = document.getElementById("carouselTrack");
    const prevBtn = document.getElementById("prevBtn");
    const nextBtn = document.getElementById("nextBtn");

    if (!track || !prevBtn || !nextBtn) return;

    const cards = track.children;
    const totalCards = cards.length;
    let currentIndex = 0;

    // Calculate how many cards to show based on screen size
    function getCardsPerView() {
      const width = window.innerWidth;
      if (width >= 1200) return 4;
      if (width >= 768) return 3;
      if (width >= 640) return 2;
      return 1;
    }

    let cardsPerView = getCardsPerView();
    const maxIndex = Math.max(0, totalCards - cardsPerView);

    // Update carousel position
    function updateCarousel() {
      const cardWidth = cards[0]?.offsetWidth || 0;
      const gap = 16; // 1rem gap
      const translateX = -(currentIndex * (cardWidth + gap));
      track.style.transform = `translateX(${translateX}px)`;

      // Update button states
      prevBtn.disabled = currentIndex === 0;
      nextBtn.disabled = currentIndex >= maxIndex;
    }

    // Previous slide
    function prevSlide() {
      if (currentIndex > 0) {
        currentIndex--;
        updateCarousel();
      }
    }

    // Next slide
    function nextSlide() {
      if (currentIndex < maxIndex) {
        currentIndex++;
        updateCarousel();
      }
    }

    // Event listeners
    prevBtn.addEventListener("click", prevSlide);
    nextBtn.addEventListener("click", nextSlide);

    // Handle resize
    function handleResize() {
      const newCardsPerView = getCardsPerView();
      if (newCardsPerView !== cardsPerView) {
        cardsPerView = newCardsPerView;
        const newMaxIndex = Math.max(0, totalCards - cardsPerView);

        // Adjust current index if needed
        if (currentIndex > newMaxIndex) {
          currentIndex = newMaxIndex;
        }

        updateCarousel();
      } else {
        updateCarousel();
      }
    }

    window.addEventListener("resize", handleResize);

    updateCarousel();

    // Touch/swipe support for mobile
    let startX = 0;
    let isDragging = false;

    track.addEventListener("touchstart", (e) => {
      startX = e.touches[0].clientX;
      isDragging = true;
    });

    track.addEventListener("touchmove", (e) => {
      if (!isDragging) return;
      e.preventDefault();
    });

    track.addEventListener("touchend", (e) => {
      if (!isDragging) return;
      isDragging = false;

      const endX = e.changedTouches[0].clientX;
      const diff = startX - endX;

      if (Math.abs(diff) > 50) {
        // Minimum swipe distance
        if (diff > 0) {
          nextSlide();
        } else {
          prevSlide();
        }
      }
    });
  });
</script>
